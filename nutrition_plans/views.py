from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponse, JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.core.cache import cache
from .models import NutritionPlan, Meal, MealItem, Food
from .forms import NutritionPlanForm, MealForm, FoodForm, NutritionPlanSearchForm, FoodSearchForm
from patients.models import Patient
from communications.services import whatsapp_service


@login_required
def send_plan_whatsapp(request, plan_id):
    """إرسال الخطة الغذائية عبر الواتساب"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    plan = get_object_or_404(NutritionPlan, id=plan_id, doctor=request.user)

    if request.method == 'POST':
        # إرسال الخطة عبر الواتساب
        success, error_message = whatsapp_service.send_nutrition_plan(plan.patient, plan)

        if success:
            messages.success(request, _('تم إرسال الخطة الغذائية عبر الواتساب بنجاح!'))
        else:
            messages.error(request, _('فشل في إرسال الخطة: {}').format(error_message))

        return redirect('nutrition_plans:nutrition_plan_detail', plan_id=plan.id)

    context = {
        'plan': plan,
    }
    return render(request, 'nutrition_plans/send_whatsapp_confirm.html', context)


@login_required
def nutrition_plan_list(request):
    """قائمة الخطط الغذائية"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    # الحصول على الخطط التابعة للطبيب (محسن للأداء)
    plans = NutritionPlan.objects.filter(doctor=request.user).select_related('patient__user').order_by('-created_at')

    # البحث والتصفية
    search_form = NutritionPlanSearchForm(request.GET, doctor=request.user)
    if search_form.is_valid():
        search_query = search_form.cleaned_data.get('search')
        status_filter = search_form.cleaned_data.get('status')
        patient_filter = search_form.cleaned_data.get('patient')

        if search_query:
            plans = plans.filter(
                Q(title__icontains=search_query) |
                Q(patient__user__first_name__icontains=search_query) |
                Q(patient__user__last_name__icontains=search_query)
            )

        if status_filter:
            plans = plans.filter(status=status_filter)

        if patient_filter:
            plans = plans.filter(patient=patient_filter)

    # الترقيم
    paginator = Paginator(plans, 10)
    page_number = request.GET.get('page')
    plans_page = paginator.get_page(page_number)

    context = {
        'plans': plans_page,
        'search_form': search_form,
    }
    return render(request, 'nutrition_plans/nutrition_plan_list.html', context)

@login_required
def select_patient_for_plan(request):
    """اختيار مريض لإنشاء خطة غذائية"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    # الحصول على المرضى الخاصين بالطبيب
    patients = Patient.objects.filter(doctor=request.user).select_related('user').order_by('user__first_name')

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        patients = patients.filter(
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query) |
            Q(user__email__icontains=search_query)
        )

    # الترقيم
    paginator = Paginator(patients, 12)
    page_number = request.GET.get('page')
    patients_page = paginator.get_page(page_number)

    context = {
        'patients': patients_page,
        'search_query': search_query,
    }
    return render(request, 'nutrition_plans/select_patient_for_plan.html', context)


@login_required
def create_nutrition_plan(request, patient_id):
    """إنشاء خطة غذائية"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    patient = get_object_or_404(Patient, id=patient_id, doctor=request.user)

    if request.method == 'POST':
        form = NutritionPlanForm(request.POST, doctor=request.user)
        if form.is_valid():
            plan = form.save(commit=False)
            plan.doctor = request.user
            plan.save()

            # إنشاء وجبات مقترحة تلقائياً
            create_suggested_meals(plan)

            messages.success(request, _('تم إنشاء الخطة الغذائية مع الوجبات المقترحة بنجاح!'))
            return redirect('nutrition_plans:nutrition_plan_detail', plan_id=plan.id)
        else:
            # إضافة رسائل الخطأ
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        # تعيين المريض مسبقاً في النموذج
        form = NutritionPlanForm(doctor=request.user, initial={'patient': patient})

    # تصحيح: التحقق من المرضى المتاحين
    patients = Patient.objects.filter(doctor=request.user)

    context = {
        'form': form,
        'patient': patient,
        'patients_debug': patients,  # للتصحيح
        'patients_count': patients.count(),  # للتصحيح
    }
    return render(request, 'nutrition_plans/create_plan_working.html', context)

@login_required
def nutrition_plan_detail(request, plan_id):
    """تفاصيل الخطة الغذائية"""
    plan = get_object_or_404(NutritionPlan, id=plan_id)

    # التحقق من الصلاحيات
    if request.user.user_type == 'doctor':
        if plan.doctor != request.user:
            messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الخطة.'))
            return redirect('nutrition_plans:nutrition_plan_list')
    elif request.user.user_type == 'patient':
        try:
            if plan.patient != request.user.patient_profile:
                messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الخطة.'))
                return redirect('patient_dashboard')
        except:
            messages.error(request, _('لم يتم العثور على ملف المريض.'))
            return redirect('home')
    else:
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    # الحصول على الوجبات مع الأطعمة (محسن للأداء)
    meals = plan.meals.select_related().prefetch_related(
        'meal_items__food'
    ).order_by('time_to_eat')

    # حساب إجمالي القيم الغذائية (محسن للأداء)
    total_calories = 0
    total_protein = 0
    total_carbs = 0
    total_fat = 0

    for meal in meals:
        total_calories += meal.total_calories
        total_protein += meal.total_protein
        total_carbs += meal.total_carbs
        total_fat += meal.total_fat

    context = {
        'plan': plan,
        'meals': meals,
        'total_calories': total_calories,
        'total_protein': total_protein,
        'total_carbs': total_carbs,
        'total_fat': total_fat,
    }
    return render(request, 'nutrition_plans/nutrition_plan_detail.html', context)

@login_required
def edit_nutrition_plan(request, plan_id):
    """تعديل الخطة الغذائية"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    plan = get_object_or_404(NutritionPlan, id=plan_id, doctor=request.user)

    if request.method == 'POST':
        form = NutritionPlanForm(request.POST, instance=plan)
        if form.is_valid():
            plan = form.save(commit=False)
            plan.doctor = request.user
            plan.save()
            messages.success(request, f'تم تحديث الخطة الغذائية "{plan.title}" بنجاح!')
            return redirect('nutrition_plans:nutrition_plan_detail', plan_id=plan.id)
    else:
        form = NutritionPlanForm(instance=plan)

    return render(request, 'nutrition_plans/edit_nutrition_plan.html', {'form': form, 'plan': plan})

@login_required
def delete_nutrition_plan(request, plan_id):
    """حذف الخطة الغذائية"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    plan = get_object_or_404(NutritionPlan, id=plan_id, doctor=request.user)

    if request.method == 'POST':
        plan_title = plan.title
        plan.delete()
        messages.success(request, f'تم حذف الخطة الغذائية "{plan_title}" بنجاح!')
        return redirect('nutrition_plans:nutrition_plan_list')

    return render(request, 'nutrition_plans/delete_nutrition_plan.html', {'plan': plan})

@login_required
def add_meal(request, plan_id):
    """إضافة وجبة"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    plan = get_object_or_404(NutritionPlan, id=plan_id, doctor=request.user)

    if request.method == 'POST':
        form = MealForm(request.POST)
        if form.is_valid():
            meal = form.save(commit=False)
            meal.nutrition_plan = plan
            meal.save()
            messages.success(request, _('تم إضافة الوجبة بنجاح!'))
            return redirect('nutrition_plans:nutrition_plan_detail', plan_id=plan.id)
    else:
        form = MealForm()

    context = {
        'form': form,
        'plan': plan,
    }
    return render(request, 'nutrition_plans/add_meal.html', context)

@login_required
def edit_meal(request, meal_id):
    """تعديل وجبة مع إضافة أطعمة"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    meal = get_object_or_404(Meal, id=meal_id, nutrition_plan__doctor=request.user)

    if request.method == 'POST':
        # تحديث بيانات الوجبة
        meal_form = MealForm(request.POST, instance=meal)

        # إضافة طعام جديد
        if 'add_food' in request.POST:
            food_id = request.POST.get('food_id')
            quantity = request.POST.get('quantity')
            notes = request.POST.get('notes', '')

            if food_id and quantity:
                try:
                    food = Food.objects.get(id=food_id)
                    MealItem.objects.create(
                        meal=meal,
                        food=food,
                        quantity=float(quantity),
                        notes=notes
                    )
                    messages.success(request, f'تم إضافة {food.name_ar} للوجبة!')
                except (Food.DoesNotExist, ValueError):
                    messages.error(request, 'حدث خطأ في إضافة الطعام.')

        # حذف طعام
        elif 'delete_item' in request.POST:
            item_id = request.POST.get('item_id')
            try:
                item = MealItem.objects.get(id=item_id, meal=meal)
                item.delete()
                messages.success(request, 'تم حذف الطعام من الوجبة!')
            except MealItem.DoesNotExist:
                messages.error(request, 'لم يتم العثور على الطعام.')

        # تحديث بيانات الوجبة
        elif meal_form.is_valid():
            meal_form.save()
            messages.success(request, 'تم تحديث بيانات الوجبة!')

        return redirect('nutrition_plans:edit_meal', meal_id=meal.id)

    else:
        meal_form = MealForm(instance=meal)

    # الحصول على الأطعمة مع caching (محسن للأداء)
    foods = cache.get('foods_list')
    if foods is None:
        foods = list(Food.objects.only('id', 'name_ar', 'name_en', 'category', 'calories_per_100g', 'protein_per_100g', 'carbs_per_100g', 'fat_per_100g').order_by('name_ar')[:50])
        cache.set('foods_list', foods, 300)  # cache لمدة 5 دقائق

    # عناصر الوجبة الحالية (محسن للأداء)
    meal_items = meal.meal_items.select_related('food').all()

    context = {
        'meal': meal,
        'meal_form': meal_form,
        'foods': foods,
        'meal_items': meal_items,
    }
    return render(request, 'nutrition_plans/edit_meal_simple.html', context)

@login_required
def delete_meal(request, meal_id):
    """حذف وجبة - مؤقت"""
    return HttpResponse("حذف وجبة - قيد التطوير")

@login_required
def export_plan_pdf(request, plan_id):
    """تصدير الخطة كـ PDF - مؤقت"""
    return HttpResponse("تصدير PDF - قيد التطوير")

@login_required
def food_list(request):
    """قائمة الأطعمة"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    # الحصول على جميع الأطعمة (محسن للأداء)
    foods = Food.objects.only('id', 'name_ar', 'name_en', 'category', 'calories_per_100g').order_by('name_ar')

    # البحث والتصفية
    search_form = FoodSearchForm(request.GET)
    if search_form.is_valid():
        search_query = search_form.cleaned_data.get('search')
        category_filter = search_form.cleaned_data.get('category')

        if search_query:
            foods = foods.filter(
                Q(name_ar__icontains=search_query) |
                Q(name_en__icontains=search_query) |
                Q(name_ku__icontains=search_query)
            )

        if category_filter:
            foods = foods.filter(category=category_filter)

    # الترقيم
    paginator = Paginator(foods, 20)
    page_number = request.GET.get('page')
    foods_page = paginator.get_page(page_number)

    context = {
        'foods': foods_page,
        'search_form': search_form,
    }
    return render(request, 'nutrition_plans/food_list.html', context)

@login_required
def add_food(request):
    """إضافة طعام جديد"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    if request.method == 'POST':
        form = FoodForm(request.POST)
        if form.is_valid():
            food = form.save()
            # مسح cache الأطعمة
            cache.delete('foods_list')
            messages.success(request, f'تم إضافة الطعام "{food.name_ar}" بنجاح!')
            return redirect('nutrition_plans:food_list')
    else:
        form = FoodForm()

    return render(request, 'nutrition_plans/add_food.html', {'form': form})

@login_required
def edit_food(request, food_id):
    """تعديل طعام"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    food = get_object_or_404(Food, id=food_id)

    if request.method == 'POST':
        form = FoodForm(request.POST, instance=food)
        if form.is_valid():
            food = form.save()
            # مسح cache الأطعمة
            cache.delete('foods_list')
            messages.success(request, f'تم تحديث الطعام "{food.name_ar}" بنجاح!')
            return redirect('nutrition_plans:food_list')
    else:
        form = FoodForm(instance=food)

    return render(request, 'nutrition_plans/edit_food.html', {'form': form, 'food': food})


@login_required
def delete_food(request, food_id):
    """حذف طعام"""
    if request.user.user_type != 'doctor':
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة.'))
        return redirect('home')

    food = get_object_or_404(Food, id=food_id)

    if request.method == 'POST':
        food_name = food.name_ar
        food.delete()
        # مسح cache الأطعمة
        cache.delete('foods_list')
        messages.success(request, f'تم حذف الطعام "{food_name}" بنجاح!')
        return redirect('nutrition_plans:food_list')

    return render(request, 'nutrition_plans/delete_food.html', {'food': food})


@login_required
def debug_plan(request, plan_id):
    """صفحة تصحيح للخطة الغذائية"""
    plan = get_object_or_404(NutritionPlan, id=plan_id)

    debug_info = []
    debug_info.append(f"الخطة: {plan.title}")
    debug_info.append(f"عدد الوجبات: {plan.meals.count()}")

    for meal in plan.meals.all():
        debug_info.append(f"\nالوجبة: {meal.name}")
        debug_info.append(f"عدد الأطعمة: {meal.meal_items.count()}")
        for item in meal.meal_items.all():
            debug_info.append(f"  - {item.food.name_ar} ({item.quantity}غ)")

    return HttpResponse("<br>".join(debug_info))


@login_required
def create_test_patients(request):
    """إنشاء مرضى للاختبار"""
    if request.user.user_type != 'doctor':
        return HttpResponse("ليس لديك صلاحية")

    from accounts.models import User
    from patients.models import Patient

    debug_info = []
    doctor = request.user

    debug_info.append(f"<h2>إنشاء مرضى للطبيب: {doctor.get_full_name()}</h2>")
    debug_info.append(f"معرف الطبيب: {doctor.id}<br>")

    # حذف المرضى الموجودين أولاً (للاختبار)
    existing_patients = Patient.objects.filter(doctor=doctor)
    debug_info.append(f"المرضى الموجودين: {existing_patients.count()}<br>")

    # أسماء المرضى الجدد
    names = [
        ('أحمد', 'محمد'),
        ('فاطمة', 'علي'),
        ('محمد', 'حسن'),
        ('زينب', 'أحمد'),
        ('علي', 'محمود')
    ]

    debug_info.append("<br><strong>إنشاء مرضى جدد:</strong><br>")

    for i, (first_name, last_name) in enumerate(names, start=1):
        username = f'test_patient_{i}'

        # حذف المستخدم إذا كان موجوداً
        User.objects.filter(username=username).delete()

        try:
            # إنشاء مستخدم جديد
            patient_user = User.objects.create_user(
                username=username,
                email=f'{username}@clinic.com',
                password='patient123',
                first_name=first_name,
                last_name=last_name,
                user_type='patient'
            )

            # إنشاء مريض جديد
            patient = Patient.objects.create(
                user=patient_user,
                doctor=doctor,
                gender='male' if i % 2 == 0 else 'female',
                height=160 + i * 5,
                current_weight=60 + i * 10,
                target_weight=55 + i * 8,
                health_goal='weight_loss' if i % 2 == 0 else 'muscle_gain'
            )

            debug_info.append(f"✅ تم إنشاء المريض {patient.id}: {patient.user.get_full_name()} ({patient.user.username})<br>")

        except Exception as e:
            debug_info.append(f"❌ خطأ في إنشاء {username}: {e}<br>")

    # عرض جميع المرضى الحاليين
    current_patients = Patient.objects.filter(doctor=doctor)
    debug_info.append(f"<br><strong>إجمالي المرضى الآن: {current_patients.count()}</strong><br>")
    debug_info.append("<br><strong>قائمة المرضى:</strong><br>")

    for patient in current_patients.order_by('id'):
        debug_info.append(f"- المريض {patient.id}: {patient.user.get_full_name()} ({patient.user.username})<br>")

    debug_info.append(f"<br><a href='/nutrition/create/1/' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>اختبار إنشاء خطة غذائية</a>")

    return HttpResponse("".join(debug_info))


@login_required
def get_patient_info(request, patient_id):
    """جلب معلومات المريض عبر AJAX"""
    if request.user.user_type != 'doctor':
        return JsonResponse({'error': 'ليس لديك صلاحية'}, status=403)

    try:
        patient = Patient.objects.get(id=patient_id, doctor=request.user)

        data = {
            'name': patient.user.get_full_name() or patient.user.username,
            'gender': patient.get_gender_display() if patient.gender else 'غير محدد',
            'height': f"{patient.height}" if patient.height else 'غير محدد',
            'weight': f"{patient.current_weight}" if patient.current_weight else 'غير محدد',
            'goal': patient.get_health_goal_display() if patient.health_goal else 'غير محدد'
        }

        return JsonResponse(data)

    except Patient.DoesNotExist:
        return JsonResponse({'error': 'المريض غير موجود'}, status=404)


@login_required
def debug_patients(request):
    """صفحة تصحيح للمرضى"""
    if request.user.user_type != 'doctor':
        return HttpResponse("ليس لديك صلاحية")

    from accounts.models import User
    from patients.models import Patient

    debug_info = []
    doctor = request.user

    debug_info.append(f"<h2>تصحيح المرضى للطبيب: {doctor.get_full_name()}</h2>")
    debug_info.append(f"<strong>معرف الطبيب:</strong> {doctor.id}")
    debug_info.append(f"<strong>اسم المستخدم:</strong> {doctor.username}")
    debug_info.append(f"<strong>نوع المستخدم:</strong> {doctor.user_type}")

    # جميع المستخدمين
    all_users = User.objects.all()
    debug_info.append(f"<br><strong>إجمالي المستخدمين:</strong> {all_users.count()}")

    # جميع المرضى
    all_patients = Patient.objects.all()
    debug_info.append(f"<strong>إجمالي المرضى:</strong> {all_patients.count()}")

    # مرضى هذا الطبيب
    doctor_patients = Patient.objects.filter(doctor=doctor)
    debug_info.append(f"<strong>مرضى هذا الطبيب:</strong> {doctor_patients.count()}")

    debug_info.append("<br><h3>تفاصيل جميع المرضى:</h3>")
    for patient in all_patients:
        debug_info.append(f"<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>")
        debug_info.append(f"<strong>المريض {patient.id}:</strong><br>")
        debug_info.append(f"- اسم المستخدم: {patient.user.username}<br>")
        debug_info.append(f"- الاسم الأول: {patient.user.first_name}<br>")
        debug_info.append(f"- الاسم الأخير: {patient.user.last_name}<br>")
        debug_info.append(f"- الاسم الكامل: {patient.user.get_full_name()}<br>")
        debug_info.append(f"- __str__: {str(patient)}<br>")
        debug_info.append(f"- الطبيب: {patient.doctor.username} (ID: {patient.doctor.id})<br>")
        debug_info.append(f"- هل ينتمي لهذا الطبيب؟ {'نعم' if patient.doctor == doctor else 'لا'}<br>")
        debug_info.append(f"</div>")

    debug_info.append("<br><h3>اختبار النموذج:</h3>")
    from .forms import NutritionPlanForm
    form = NutritionPlanForm(doctor=doctor)
    queryset = form.fields['patient'].queryset
    debug_info.append(f"<strong>عدد المرضى في النموذج:</strong> {queryset.count()}<br>")
    for patient in queryset:
        debug_info.append(f"- {patient.id}: {str(patient)}<br>")

    return HttpResponse("<br>".join(debug_info))


@login_required
def test_patients(request):
    """صفحة اختبار المرضى"""
    if request.user.user_type != 'doctor':
        return HttpResponse("ليس لديك صلاحية")

    doctor = request.user
    patients = Patient.objects.filter(doctor=doctor)
    patient = patients.first() if patients.exists() else None

    context = {
        'patients_debug': patients,
        'patients_count': patients.count(),
        'patient': patient,
    }
    return render(request, 'nutrition_plans/test_patients.html', context)


def create_suggested_meals(plan):
    """إنشاء وجبات مقترحة للخطة الغذائية"""
    from datetime import time

    # الحصول على الأطعمة المتاحة باستخدام filter بدلاً من get
    try:
        # أطعمة الإفطار
        bread = Food.objects.filter(name_ar__icontains='خبز').first()
        yogurt = Food.objects.filter(name_ar__icontains='زبادي').first()
        banana = Food.objects.filter(name_ar__icontains='موز').first()

        # أطعمة الغداء
        rice = Food.objects.filter(name_ar__icontains='أرز').first()
        chicken = Food.objects.filter(name_ar__icontains='دجاج').first()
        vegetables = Food.objects.filter(name_ar__icontains='خضروات').first()

        # أطعمة العشاء
        salmon = Food.objects.filter(name_ar__icontains='سلمون').first()
        apple = Food.objects.filter(name_ar__icontains='تفاح').first()

        # التحقق من وجود جميع الأطعمة المطلوبة
        if not all([bread, yogurt, banana, rice, chicken, vegetables, salmon, apple]):
            # إذا لم توجد بعض الأطعمة، لا ننشئ وجبات
            return

    except Exception as e:
        # في حالة حدوث أي خطأ، لا ننشئ وجبات
        return

    # إنشاء وجبة الإفطار المقترحة
    breakfast = Meal.objects.create(
        nutrition_plan=plan,
        name="إفطار صحي مقترح",
        meal_type="breakfast",
        day_number=1,
        time_to_eat=time(8, 0),
        instructions="إفطار متوازن يحتوي على الكربوهيدرات والبروتين والفواكه"
    )

    # إضافة أطعمة الإفطار
    MealItem.objects.create(meal=breakfast, food=bread, quantity=50, notes="شريحتان")
    MealItem.objects.create(meal=breakfast, food=yogurt, quantity=150, notes="كوب متوسط")
    MealItem.objects.create(meal=breakfast, food=banana, quantity=100, notes="حبة متوسطة")

    # إنشاء وجبة الغداء المقترحة
    lunch = Meal.objects.create(
        nutrition_plan=plan,
        name="غداء متوازن مقترح",
        meal_type="lunch",
        day_number=1,
        time_to_eat=time(13, 0),
        instructions="وجبة غداء غنية بالبروتين والكربوهيدرات والخضروات"
    )

    # إضافة أطعمة الغداء
    MealItem.objects.create(meal=lunch, food=rice, quantity=150, notes="كوب مطبوخ")
    MealItem.objects.create(meal=lunch, food=chicken, quantity=120, notes="قطعة متوسطة")
    MealItem.objects.create(meal=lunch, food=vegetables, quantity=200, notes="طبق سلطة")

    # إنشاء وجبة العشاء المقترحة
    dinner = Meal.objects.create(
        nutrition_plan=plan,
        name="عشاء خفيف مقترح",
        meal_type="dinner",
        day_number=1,
        time_to_eat=time(19, 0),
        instructions="عشاء خفيف وصحي غني بالبروتين"
    )

    # إضافة أطعمة العشاء
    MealItem.objects.create(meal=dinner, food=salmon, quantity=100, notes="قطعة صغيرة")
    MealItem.objects.create(meal=dinner, food=vegetables, quantity=150, notes="خضروات مطبوخة")
    MealItem.objects.create(meal=dinner, food=apple, quantity=150, notes="حبة كبيرة")

    # إنشاء وجبة خفيفة صباحية
    morning_snack = Meal.objects.create(
        nutrition_plan=plan,
        name="وجبة خفيفة صباحية",
        meal_type="morning_snack",
        day_number=1,
        time_to_eat=time(10, 30),
        instructions="وجبة خفيفة صحية بين الإفطار والغداء"
    )

    MealItem.objects.create(meal=morning_snack, food=apple, quantity=100, notes="حبة متوسطة")

    # إنشاء وجبة خفيفة بعد الظهر
    afternoon_snack = Meal.objects.create(
        nutrition_plan=plan,
        name="وجبة خفيفة بعد الظهر",
        meal_type="afternoon_snack",
        day_number=1,
        time_to_eat=time(16, 0),
        instructions="وجبة خفيفة لتجديد الطاقة"
    )

    MealItem.objects.create(meal=afternoon_snack, food=yogurt, quantity=100, notes="كوب صغير")
    MealItem.objects.create(meal=afternoon_snack, food=banana, quantity=80, notes="نصف حبة")
