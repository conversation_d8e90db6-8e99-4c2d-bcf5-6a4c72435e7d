from django.core.management.base import BaseCommand
from nutrition_plans.models import Food


class Command(BaseCommand):
    help = 'Load sample foods data'

    def handle(self, *args, **options):
        sample_foods = [
            # بروتينات
            {
                'name_ar': 'صدر دجاج مشوي',
                'name_en': 'Grilled Chicken Breast',
                'name_ku': 'سنگی مریشک برژاو',
                'category': 'protein',
                'calories_per_100g': 165,
                'protein_per_100g': 31,
                'carbs_per_100g': 0,
                'fat_per_100g': 3.6,
                'fiber_per_100g': 0
            },
            {
                'name_ar': 'لحم بقري مشوي',
                'name_en': 'Grilled Beef',
                'name_ku': 'گۆشتی گا برژاو',
                'category': 'protein',
                'calories_per_100g': 250,
                'protein_per_100g': 26,
                'carbs_per_100g': 0,
                'fat_per_100g': 15,
                'fiber_per_100g': 0
            },
            {
                'name_ar': 'سمك السلمون',
                'name_en': 'Salmon',
                'name_ku': 'ماسی سەلمۆن',
                'category': 'protein',
                'calories_per_100g': 208,
                'protein_per_100g': 22,
                'carbs_per_100g': 0,
                'fat_per_100g': 13,
                'fiber_per_100g': 0
            },
            {
                'name_ar': 'بيض مسلوق',
                'name_en': 'Boiled Eggs',
                'name_ku': 'هێلکە کوڵاو',
                'category': 'protein',
                'calories_per_100g': 155,
                'protein_per_100g': 13,
                'carbs_per_100g': 1.1,
                'fat_per_100g': 11,
                'fiber_per_100g': 0
            },
            
            # حبوب ونشويات
            {
                'name_ar': 'أرز أبيض مطبوخ',
                'name_en': 'Cooked White Rice',
                'name_ku': 'برنج سپی کوڵاو',
                'category': 'grains',
                'calories_per_100g': 130,
                'protein_per_100g': 2.7,
                'carbs_per_100g': 28,
                'fat_per_100g': 0.3,
                'fiber_per_100g': 0.4
            },
            {
                'name_ar': 'خبز أسمر',
                'name_en': 'Brown Bread',
                'name_ku': 'نانی قاوەیی',
                'category': 'grains',
                'calories_per_100g': 247,
                'protein_per_100g': 13,
                'carbs_per_100g': 41,
                'fat_per_100g': 4.2,
                'fiber_per_100g': 7
            },
            {
                'name_ar': 'شوفان',
                'name_en': 'Oats',
                'name_ku': 'جۆ',
                'category': 'grains',
                'calories_per_100g': 389,
                'protein_per_100g': 16.9,
                'carbs_per_100g': 66.3,
                'fat_per_100g': 6.9,
                'fiber_per_100g': 10.6
            },
            
            # خضروات
            {
                'name_ar': 'بروكلي',
                'name_en': 'Broccoli',
                'name_ku': 'بڕۆکلی',
                'category': 'vegetables',
                'calories_per_100g': 34,
                'protein_per_100g': 2.8,
                'carbs_per_100g': 7,
                'fat_per_100g': 0.4,
                'fiber_per_100g': 2.6
            },
            {
                'name_ar': 'سبانخ',
                'name_en': 'Spinach',
                'name_ku': 'سەوزە',
                'category': 'vegetables',
                'calories_per_100g': 23,
                'protein_per_100g': 2.9,
                'carbs_per_100g': 3.6,
                'fat_per_100g': 0.4,
                'fiber_per_100g': 2.2
            },
            {
                'name_ar': 'جزر',
                'name_en': 'Carrots',
                'name_ku': 'گەزەر',
                'category': 'vegetables',
                'calories_per_100g': 41,
                'protein_per_100g': 0.9,
                'carbs_per_100g': 9.6,
                'fat_per_100g': 0.2,
                'fiber_per_100g': 2.8
            },
            
            # فواكه
            {
                'name_ar': 'تفاح',
                'name_en': 'Apple',
                'name_ku': 'سێو',
                'category': 'fruits',
                'calories_per_100g': 52,
                'protein_per_100g': 0.3,
                'carbs_per_100g': 14,
                'fat_per_100g': 0.2,
                'fiber_per_100g': 2.4
            },
            {
                'name_ar': 'موز',
                'name_en': 'Banana',
                'name_ku': 'مۆز',
                'category': 'fruits',
                'calories_per_100g': 89,
                'protein_per_100g': 1.1,
                'carbs_per_100g': 23,
                'fat_per_100g': 0.3,
                'fiber_per_100g': 2.6
            },
            {
                'name_ar': 'برتقال',
                'name_en': 'Orange',
                'name_ku': 'پرتەقاڵ',
                'category': 'fruits',
                'calories_per_100g': 47,
                'protein_per_100g': 0.9,
                'carbs_per_100g': 12,
                'fat_per_100g': 0.1,
                'fiber_per_100g': 2.4
            },
            
            # منتجات الألبان
            {
                'name_ar': 'حليب قليل الدسم',
                'name_en': 'Low-fat Milk',
                'name_ku': 'شیری کەم چەوری',
                'category': 'dairy',
                'calories_per_100g': 42,
                'protein_per_100g': 3.4,
                'carbs_per_100g': 5,
                'fat_per_100g': 1,
                'fiber_per_100g': 0
            },
            {
                'name_ar': 'جبن قريش',
                'name_en': 'Cottage Cheese',
                'name_ku': 'پەنیری تازە',
                'category': 'dairy',
                'calories_per_100g': 98,
                'protein_per_100g': 11,
                'carbs_per_100g': 3.4,
                'fat_per_100g': 4.3,
                'fiber_per_100g': 0
            },
            {
                'name_ar': 'زبادي يوناني',
                'name_en': 'Greek Yogurt',
                'name_ku': 'ماستی یۆنانی',
                'category': 'dairy',
                'calories_per_100g': 59,
                'protein_per_100g': 10,
                'carbs_per_100g': 3.6,
                'fat_per_100g': 0.4,
                'fiber_per_100g': 0
            },
            
            # دهون وزيوت
            {
                'name_ar': 'زيت زيتون',
                'name_en': 'Olive Oil',
                'name_ku': 'زەیتی زەیتوون',
                'category': 'fats',
                'calories_per_100g': 884,
                'protein_per_100g': 0,
                'carbs_per_100g': 0,
                'fat_per_100g': 100,
                'fiber_per_100g': 0
            },
            {
                'name_ar': 'أفوكادو',
                'name_en': 'Avocado',
                'name_ku': 'ئەڤۆکادۆ',
                'category': 'fats',
                'calories_per_100g': 160,
                'protein_per_100g': 2,
                'carbs_per_100g': 9,
                'fat_per_100g': 15,
                'fiber_per_100g': 7
            },
            
            # وجبات خفيفة
            {
                'name_ar': 'لوز',
                'name_en': 'Almonds',
                'name_ku': 'بادەم',
                'category': 'snacks',
                'calories_per_100g': 579,
                'protein_per_100g': 21,
                'carbs_per_100g': 22,
                'fat_per_100g': 50,
                'fiber_per_100g': 12
            },
            {
                'name_ar': 'جوز',
                'name_en': 'Walnuts',
                'name_ku': 'گوێز',
                'category': 'snacks',
                'calories_per_100g': 654,
                'protein_per_100g': 15,
                'carbs_per_100g': 14,
                'fat_per_100g': 65,
                'fiber_per_100g': 7
            }
        ]

        created_count = 0
        for food_data in sample_foods:
            food, created = Food.objects.get_or_create(
                name_ar=food_data['name_ar'],
                defaults=food_data
            )
            if created:
                created_count += 1
                self.stdout.write(f"تم إنشاء: {food.name_ar}")
            else:
                self.stdout.write(f"موجود مسبقاً: {food.name_ar}")

        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {created_count} طعام جديد من أصل {len(sample_foods)}')
        )
