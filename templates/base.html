<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة العيادات الغذائية{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    {% load static %}
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        .btn-success {
            background-color: #198754;
            border-color: #198754;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #fff;
            border-left: 1px solid #dee2e6;
        }
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
        }
        .sidebar .nav-link:hover {
            background-color: #f8f9fa;
            color: #0d6efd;
        }
        .sidebar .nav-link.active {
            background-color: #0d6efd;
            color: white;
        }
        .main-content {
            padding: 2rem;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stats-card .card-body {
            padding: 1.5rem;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }

        /* Language selector styles */
        .dropdown-item-form {
            margin: 0;
            padding: 0;
        }

        .dropdown-item-form button {
            background: none;
            border: none;
            width: 100%;
            text-align: right;
            padding: 0.375rem 1rem;
            color: #212529;
            text-decoration: none;
            display: block;
            clear: both;
            font-weight: 400;
            white-space: nowrap;
        }

        .dropdown-item-form button:hover {
            background-color: #e9ecef;
            color: #16181b;
        }

        .dropdown-item-form button.active {
            background-color: #0d6efd;
            color: white;
        }

        /* Enhanced card styles */
        .card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        /* Stats cards */
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
        }

        .stats-card.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        .stats-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stats-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        /* Button enhancements */
        .btn {
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
        }

        /* Form enhancements */
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        /* Navigation enhancements */
        .navbar {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .sidebar {
            box-shadow: 0.125rem 0 0.25rem rgba(0, 0, 0, 0.075);
        }

        /* Loading animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .stats-number {
                font-size: 1.5rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-heartbeat me-2"></i>
                نظام إدارة العيادات الغذائية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_authenticated %}
                        {% if user.user_type == 'doctor' %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'doctor_dashboard' %}">
                                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'simple_patients' %}">
                                    <i class="fas fa-users me-1"></i>المرضى
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/nutrition/">
                                    <i class="fas fa-utensils me-1"></i>الخطط الغذائية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'simple_messages' %}">
                                    <i class="fas fa-calendar me-1"></i>المواعيد
                                </a>
                            </li>
                        {% elif user.user_type == 'patient' %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'patient_dashboard' %}">
                                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'simple_messages' %}">
                                    <i class="fas fa-envelope me-1"></i>الرسائل
                                </a>
                            </li>
                        {% endif %}
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <!-- Language Selector -->
                        <li class="nav-item dropdown me-3">
                            {% load i18n %}
                            <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-globe me-1"></i>
                                {% get_current_language as LANGUAGE_CODE %}
                                <span id="current-language-display">
                                    {% if LANGUAGE_CODE == 'ar' %}العربية
                                    {% elif LANGUAGE_CODE == 'en' %}English
                                    {% elif LANGUAGE_CODE == 'ku' %}کوردی
                                    {% else %}{{ LANGUAGE_CODE }}{% endif %}
                                </span>
                                <small class="text-muted">({{ LANGUAGE_CODE }})</small>
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                                <li>
                                    <form method="post" action="/i18n/setlang/" style="margin: 0;">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="ar">
                                        <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                        <button type="submit" class="dropdown-item {% if LANGUAGE_CODE == 'ar' %}active{% endif %}" style="background: none; border: none; width: 100%; text-align: right;">
                                            🇮🇶 العربية
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form method="post" action="/i18n/setlang/" style="margin: 0;">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="en">
                                        <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                        <button type="submit" class="dropdown-item {% if LANGUAGE_CODE == 'en' %}active{% endif %}" style="background: none; border: none; width: 100%; text-align: right;">
                                            🇺🇸 English
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form method="post" action="/i18n/setlang/" style="margin: 0;">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="ku">
                                        <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                        <button type="submit" class="dropdown-item {% if LANGUAGE_CODE == 'ku' %}active{% endif %}" style="background: none; border: none; width: 100%; text-align: right;">
                                            🟡🔴🟢 کوردی
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ user.get_full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'profile' %}">
                                    <i class="fas fa-user-edit me-1"></i>الملف الشخصي
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'logout' %}">
                                    <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'register_choice' %}">
                                <i class="fas fa-user-plus me-1"></i>تسجيل جديد
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container-fluid mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            {% if user.is_authenticated and user.user_type == 'doctor' %}
                <!-- Sidebar for Doctor -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'doctor_dashboard' %}">
                                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'simple_patients' %}">
                                    <i class="fas fa-users me-2"></i>إدارة المرضى
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/nutrition/">
                                    <i class="fas fa-utensils me-2"></i>الخطط الغذائية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'nutrition_plans:food_list' %}">
                                    <i class="fas fa-apple-alt me-2"></i>إدارة الأطعمة
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'simple_messages' %}">
                                    <i class="fas fa-calendar me-2"></i>المواعيد
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'simple_messages' %}">
                                    <i class="fas fa-envelope me-2"></i>الرسائل
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'simple_messages' %}">
                                    <i class="fab fa-whatsapp me-2"></i>رسائل الواتساب
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>

                <!-- Main content area -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            {% else %}
                <!-- Full width for non-doctor users -->
                <main class="col-12 main-content">
            {% endif %}
                    {% block content %}{% endblock %}
                </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // دالة تغيير اللغة
        function changeLanguage(languageCode) {
            console.log('تغيير اللغة إلى:', languageCode);

            // حفظ اللغة في localStorage
            localStorage.setItem('selectedLanguage', languageCode);

            // حفظ اللغة في cookie مع إعدادات صحيحة
            const expires = new Date();
            expires.setFullYear(expires.getFullYear() + 1); // سنة واحدة
            document.cookie = `django_language=${languageCode}; path=/; expires=${expires.toUTCString()}; SameSite=Lax`;

            console.log('تم حفظ اللغة في cookie:', document.cookie);

            // إعادة تحميل الصفحة
            setTimeout(() => {
                window.location.reload();
            }, 100);
        }

        // دالة للحصول على قيمة cookie
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // عرض معلومات اللغة الحالية
            const currentLang = getCookie('django_language');
            console.log('اللغة الحالية من cookie:', currentLang);
            console.log('جميع cookies:', document.cookie);

            // إضافة تأثيرات hover للبطاقات
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.2s ease';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
