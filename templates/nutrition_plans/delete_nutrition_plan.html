{% extends 'base.html' %}

{% block title %}حذف {{ plan.title }} - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>تأكيد حذف الخطة الغذائية</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-warning me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <p class="mb-4">هل أنت متأكد من حذف الخطة الغذائية التالية؟</p>

                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>عنوان الخطة:</strong></td>
                                            <td>{{ plan.title }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>المريض:</strong></td>
                                            <td>{{ plan.patient.user.get_full_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ البداية:</strong></td>
                                            <td>{{ plan.start_date|date:"Y-m-d" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ النهاية:</strong></td>
                                            <td>{{ plan.end_date|date:"Y-m-d"|default:"غير محدد" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                <span class="badge bg-{% if plan.status == 'active' %}success{% elif plan.status == 'completed' %}primary{% else %}secondary{% endif %}">
                                                    {{ plan.get_status_display }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>السعرات المستهدفة:</strong></td>
                                            <td>{{ plan.target_calories|default:"غير محدد" }} سعرة</td>
                                        </tr>
                                        <tr>
                                            <td><strong>البروتين المستهدف:</strong></td>
                                            <td>{{ plan.target_protein|default:"غير محدد" }} غ</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الكربوهيدرات المستهدفة:</strong></td>
                                            <td>{{ plan.target_carbs|default:"غير محدد" }} غ</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الدهون المستهدفة:</strong></td>
                                            <td>{{ plan.target_fat|default:"غير محدد" }} غ</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>{{ plan.created_at|date:"Y-m-d" }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            {% if plan.instructions %}
                            <div class="mt-3">
                                <strong>التعليمات:</strong>
                                <p class="text-muted">{{ plan.instructions|truncatewords:20 }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> حذف هذه الخطة سيؤدي إلى حذف جميع الوجبات والعناصر المرتبطة بها.
                    </div>

                    <!-- إحصائيات الخطة -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-primary">{{ plan.meals.count }}</h5>
                                    <small class="text-muted">عدد الوجبات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-success">
                                        {% with total_items=0 %}
                                            {% for meal in plan.meals.all %}
                                                {% with total_items=total_items|add:meal.meal_items.count %}{% endwith %}
                                            {% endfor %}
                                            {{ total_items }}
                                        {% endwith %}
                                    </h5>
                                    <small class="text-muted">عدد الأطعمة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-warning">
                                        {% if plan.start_date and plan.end_date %}
                                            {{ plan.end_date|timeuntil:plan.start_date }}
                                        {% else %}
                                            غير محدد
                                        {% endif %}
                                    </h5>
                                    <small class="text-muted">مدة الخطة</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post" class="d-flex justify-content-between">
                        {% csrf_token %}
                        <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>تأكيد الحذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
