{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}تعديل {{ plan.title }} - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-edit me-2"></i>تعديل الخطة الغذائية: {{ plan.title }}</h2>
                <div>
                    <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}" class="btn btn-outline-info me-2">
                        <i class="fas fa-eye me-2"></i>عرض الخطة
                    </a>
                    <a href="{% url 'nutrition_plans:nutrition_plan_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">تعديل معلومات الخطة الغذائية</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                <!-- معلومات أساسية -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">المعلومات الأساسية</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">عنوان الخطة *</label>
                                        {{ form.title|add_class:"form-control" }}
                                        {% if form.title.errors %}
                                            <div class="text-danger small">{{ form.title.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المريض *</label>
                                        {{ form.patient|add_class:"form-select" }}
                                        {% if form.patient.errors %}
                                            <div class="text-danger small">{{ form.patient.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ البداية *</label>
                                        {{ form.start_date|add_class:"form-control" }}
                                        {% if form.start_date.errors %}
                                            <div class="text-danger small">{{ form.start_date.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ النهاية</label>
                                        {{ form.end_date|add_class:"form-control" }}
                                        {% if form.end_date.errors %}
                                            <div class="text-danger small">{{ form.end_date.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        {{ form.status|add_class:"form-select" }}
                                        {% if form.status.errors %}
                                            <div class="text-danger small">{{ form.status.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- الأهداف الغذائية -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">الأهداف الغذائية اليومية</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">السعرات المستهدفة</label>
                                        <div class="input-group">
                                            {{ form.target_calories|add_class:"form-control" }}
                                            <span class="input-group-text">سعرة</span>
                                        </div>
                                        {% if form.target_calories.errors %}
                                            <div class="text-danger small">{{ form.target_calories.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البروتين المستهدف</label>
                                        <div class="input-group">
                                            {{ form.target_protein|add_class:"form-control" }}
                                            <span class="input-group-text">غ</span>
                                        </div>
                                        {% if form.target_protein.errors %}
                                            <div class="text-danger small">{{ form.target_protein.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الكربوهيدرات المستهدفة</label>
                                        <div class="input-group">
                                            {{ form.target_carbs|add_class:"form-control" }}
                                            <span class="input-group-text">غ</span>
                                        </div>
                                        {% if form.target_carbs.errors %}
                                            <div class="text-danger small">{{ form.target_carbs.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الدهون المستهدفة</label>
                                        <div class="input-group">
                                            {{ form.target_fat|add_class:"form-control" }}
                                            <span class="input-group-text">غ</span>
                                        </div>
                                        {% if form.target_fat.errors %}
                                            <div class="text-danger small">{{ form.target_fat.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- التعليمات -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">التعليمات والملاحظات</h6>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">تعليمات الخطة</label>
                                        {{ form.instructions|add_class:"form-control" }}
                                        {% if form.instructions.errors %}
                                            <div class="text-danger small">{{ form.instructions.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- أزرار التحكم -->
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- معلومات الخطة الحالية -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات الخطة الحالية</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>العنوان:</strong></td>
                                    <td>{{ plan.title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>المريض:</strong></td>
                                    <td>{{ plan.patient.user.get_full_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ البداية:</strong></td>
                                    <td>{{ plan.start_date|date:"Y-m-d" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ النهاية:</strong></td>
                                    <td>{{ plan.end_date|date:"Y-m-d"|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        <span class="badge bg-{% if plan.status == 'active' %}success{% elif plan.status == 'completed' %}primary{% else %}secondary{% endif %}">
                                            {{ plan.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>السعرات المستهدفة:</strong></td>
                                    <td>{{ plan.target_calories|default:"غير محدد" }} سعرة</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>{{ plan.created_at|date:"Y-m-d" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>آخر تحديث:</strong></td>
                                    <td>{{ plan.updated_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">إجراءات إضافية</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{% url 'nutrition_plans:add_meal' plan.id %}" class="btn btn-success btn-sm">
                                    <i class="fas fa-plus me-2"></i>إضافة وجبة
                                </a>
                                <a href="{% url 'nutrition_plans:export_plan_pdf' plan.id %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                </a>
                                <a href="{% url 'nutrition_plans:send_plan_whatsapp' plan.id %}" class="btn btn-success btn-sm">
                                    <i class="fab fa-whatsapp me-2"></i>إرسال واتساب
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">نصائح التعديل</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    تأكد من دقة الأهداف الغذائية
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-calendar text-warning me-2"></i>
                                    حدد تواريخ واضحة للخطة
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-save text-success me-2"></i>
                                    احفظ التغييرات بعد التأكد
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
