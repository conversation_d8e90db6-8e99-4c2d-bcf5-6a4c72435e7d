{% extends 'base.html' %}

{% block title %}{{ plan.title }} - تفاصيل الخطة الغذائية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-utensils me-2"></i>{{ plan.title }}</h2>
            <p class="text-muted mb-0">خطة غذائية لـ {{ plan.patient.user.get_full_name }}</p>
        </div>
        <div>
            {% if user.user_type == 'doctor' %}
                <a href="{% url 'nutrition_plans:add_meal' plan.id %}" class="btn btn-success me-2">
                    <i class="fas fa-plus me-2"></i>إضافة وجبة
                </a>
                <a href="{% url 'nutrition_plans:edit_nutrition_plan' plan.id %}" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-2"></i>تعديل
                </a>
                <a href="{% url 'nutrition_plans:send_plan_whatsapp' plan.id %}" class="btn btn-primary me-2">
                    <i class="fab fa-whatsapp me-2"></i>إرسال
                </a>
            {% endif %}
            <a href="{% url 'nutrition_plans:nutrition_plan_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة
            </a>
        </div>
    </div>

    <!-- Plan Info Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-fire text-danger fa-2x mb-2"></i>
                    <h4 class="text-primary">{{ plan.target_calories|default:"--" }}</h4>
                    <small class="text-muted">سعرة حرارية</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-utensils text-success fa-2x mb-2"></i>
                    <h4 class="text-success">{{ meals.count }}</h4>
                    <small class="text-muted">وجبة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-calendar text-info fa-2x mb-2"></i>
                    <h4 class="text-info">{{ plan.start_date|date:"m-d" }}</h4>
                    <small class="text-muted">تاريخ البداية</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-check-circle text-warning fa-2x mb-2"></i>
                    <span class="badge bg-{% if plan.status == 'active' %}success{% elif plan.status == 'completed' %}primary{% else %}secondary{% endif %} fs-6">
                        {{ plan.get_status_display }}
                    </span>
                    <div><small class="text-muted">حالة الخطة</small></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plan Details -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>تفاصيل الخطة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>المريض:</strong> {{ plan.patient.user.get_full_name }}</p>
                            <p><strong>الطبيب:</strong> د. {{ plan.doctor.get_full_name }}</p>
                            <p><strong>تاريخ البداية:</strong> {{ plan.start_date|date:"Y-m-d" }}</p>
                            {% if plan.end_date %}
                                <p><strong>تاريخ النهاية:</strong> {{ plan.end_date|date:"Y-m-d" }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {% if plan.target_protein %}
                                <p><strong>البروتين المستهدف:</strong> {{ plan.target_protein }} غ</p>
                            {% endif %}
                            {% if plan.target_carbs %}
                                <p><strong>الكربوهيدرات المستهدفة:</strong> {{ plan.target_carbs }} غ</p>
                            {% endif %}
                            {% if plan.target_fat %}
                                <p><strong>الدهون المستهدفة:</strong> {{ plan.target_fat }} غ</p>
                            {% endif %}
                        </div>
                    </div>
                    {% if plan.instructions %}
                        <div class="mt-3">
                            <strong>التعليمات:</strong>
                            <p class="text-muted">{{ plan.instructions }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ plan.updated_at|date:"Y-m-d" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if plan.instructions %}
                <div class="mt-3">
                    <h6 class="text-success border-bottom pb-2">التعليمات الخاصة:</h6>
                    <p class="text-muted">{{ plan.instructions }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Nutritional Targets -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bullseye me-2"></i>الأهداف الغذائية
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <div class="h4 mb-0 text-primary">{{ plan.target_calories }}</div>
                            <small class="text-muted">سعرة حرارية</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="h4 mb-0 text-success">{{ plan.target_protein }}</div>
                        <small class="text-muted">غ بروتين</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h4 mb-0 text-warning">{{ plan.target_carbs }}</div>
                            <small class="text-muted">غ كربوهيدرات</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-0 text-info">{{ plan.target_fat }}</div>
                        <small class="text-muted">غ دهون</small>
                    </div>
                </div>
                
                <hr>
                
                <h6 class="text-primary mb-2">الإجمالي الحالي:</h6>
                <div class="row text-center small">
                    <div class="col-6 mb-2">
                        <div class="border-end">
                            <div class="fw-bold">{{ total_calories|floatformat:0 }}</div>
                            <small class="text-muted">سعرة</small>
                        </div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="fw-bold">{{ total_protein|floatformat:1 }}</div>
                        <small class="text-muted">غ بروتين</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <div class="fw-bold">{{ total_carbs|floatformat:1 }}</div>
                            <small class="text-muted">غ كربوهيدرات</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="fw-bold">{{ total_fat|floatformat:1 }}</div>
                        <small class="text-muted">غ دهون</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Meals -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-light">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-utensils me-2"></i>الوجبات اليومية المقترحة
            </h5>
            {% if user.user_type == 'doctor' %}
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>يمكن تعديل جميع الوجبات والأطعمة
            </small>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        {% if user.user_type == 'doctor' %}
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-lightbulb me-2"></i>
            <strong>وجبات مقترحة:</strong> تم إنشاء هذه الوجبات تلقائياً كاقتراح أولي. يمكنك تعديل أي وجبة أو إضافة/حذف أطعمة حسب احتياجات المريض.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endif %}

        {% if meals %}
            <div class="row">
                {% for meal in meals %}
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card border h-100">
                        <div class="card-header bg-{% if meal.meal_type == 'breakfast' %}warning{% elif meal.meal_type == 'lunch' %}success{% elif meal.meal_type == 'dinner' %}info{% else %}secondary{% endif %} text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">{{ meal.name }}</h6>
                                {% if user.user_type == 'doctor' %}
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'nutrition_plans:edit_meal' meal.id %}" class="btn btn-light btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'nutrition_plans:delete_meal' meal.id %}" class="btn btn-light btn-sm text-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                            <small>{{ meal.get_meal_type_display }} - {{ meal.time_to_eat|time:"H:i" }}</small>
                        </div>
                        <div class="card-body">
                            {% if meal.meal_items.all %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الطعام</th>
                                                <th>الكمية</th>
                                                <th>السعرات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for meal_item in meal.meal_items.all %}
                                            <tr>
                                                <td>{{ meal_item.food.name_ar }}</td>
                                                <td>{{ meal_item.quantity }}غ</td>
                                                <td>{{ meal_item.calories|floatformat:0 }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <!-- إجمالي الوجبة -->
                                <div class="alert alert-info">
                                    <strong>إجمالي الوجبة:</strong> {{ meal.total_calories|floatformat:0 }} سعرة
                                </div>
                                
                                <div class="mt-3 p-2 bg-light rounded">
                                    <div class="row text-center small">
                                        <div class="col-6">
                                            <div class="fw-bold text-primary">{{ meal.total_calories|floatformat:0 }}</div>
                                            <small class="text-muted">سعرة</small>
                                        </div>
                                        <div class="col-6">
                                            <div class="fw-bold text-success">{{ meal.total_protein|floatformat:1 }}</div>
                                            <small class="text-muted">غ بروتين</small>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <p class="text-muted text-center">لا توجد أطعمة في هذه الوجبة</p>
                                {% if user.user_type == 'doctor' %}
                                <div class="text-center">
                                    <a href="{% url 'nutrition_plans:edit_meal' meal.id %}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>إضافة أطعمة
                                    </a>
                                </div>
                                {% endif %}
                            {% endif %}
                            
                            {% if meal.instructions %}
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {{ meal.instructions }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد وجبات</h5>
                <p class="text-muted">لم يتم إضافة أي وجبات لهذه الخطة بعد.</p>
                {% if user.user_type == 'doctor' %}
                <a href="{% url 'nutrition_plans:add_meal' plan.id %}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>إضافة وجبة
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
