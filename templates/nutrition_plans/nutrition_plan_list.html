{% extends 'base.html' %}

{% block title %}الخطط الغذائية - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-utensils me-2"></i>الخطط الغذائية
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'patients:patient_list' %}" class="btn btn-success me-2">
            <i class="fas fa-plus me-2"></i>إنشاء خطة جديدة
        </a>
        <a href="{% url 'nutrition_plans:food_list' %}" class="btn btn-outline-primary">
            <i class="fas fa-apple-alt me-2"></i>إدارة الأطعمة
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                {{ search_form.search }}
            </div>
            <div class="col-md-3">
                {{ search_form.status }}
            </div>
            <div class="col-md-3">
                {{ search_form.patient }}
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Plans List -->
<div class="row">
    {% if plans %}
        {% for plan in plans %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-{% if plan.status == 'active' %}success{% elif plan.status == 'inactive' %}warning{% else %}secondary{% endif %} text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="card-title mb-0">{{ plan.title }}</h6>
                        <span class="badge bg-light text-dark">{{ plan.get_status_display }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        {% if plan.patient.user.profile_picture %}
                            <img src="{{ plan.patient.user.profile_picture.url }}" class="rounded-circle me-3" width="50" height="50" alt="صورة المريض">
                        {% else %}
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                {{ plan.patient.user.first_name|first }}
                            </div>
                        {% endif %}
                        <div>
                            <h6 class="mb-0">{{ plan.patient.user.get_full_name }}</h6>
                            <small class="text-muted">{{ plan.patient.user.phone_number }}</small>
                        </div>
                    </div>
                    
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h5 mb-0 text-primary">{{ plan.target_calories }}</div>
                                <small class="text-muted">سعرة</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h5 mb-0 text-success">{{ plan.target_protein }}</div>
                            <small class="text-muted">غ بروتين</small>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center text-muted small mb-3">
                        <span>
                            <i class="fas fa-calendar me-1"></i>
                            {{ plan.start_date }} - {{ plan.end_date }}
                        </span>
                        <span>
                            <i class="fas fa-utensils me-1"></i>
                            {{ plan.meals.count }} وجبة
                        </span>
                    </div>
                    
                    {% if plan.instructions %}
                    <p class="text-muted small">{{ plan.instructions|truncatechars:100 }}</p>
                    {% endif %}
                </div>
                <div class="card-footer bg-transparent">
                    <div class="btn-group w-100">
                        <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>عرض
                        </a>
                        <a href="{% url 'nutrition_plans:edit_nutrition_plan' plan.id %}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <a href="{% url 'nutrition_plans:delete_nutrition_plan' plan.id %}" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash me-1"></i>حذف
                        </a>
                        <a href="{% url 'nutrition_plans:send_plan_whatsapp' plan.id %}" class="btn btn-outline-success btn-sm">
                            <i class="fab fa-whatsapp me-1"></i>إرسال
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        
        <!-- Pagination -->
        {% if plans.has_other_pages %}
            <div class="col-12">
                <nav aria-label="تنقل الخطط الغذائية">
                    <ul class="pagination justify-content-center">
                        {% if plans.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ plans.previous_page_number }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for num in plans.paginator.page_range %}
                            {% if plans.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > plans.number|add:'-3' and num < plans.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if plans.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ plans.next_page_number }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        {% endif %}
    {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد خطط غذائية</h5>
                <p class="text-muted">لم يتم إنشاء أي خطط غذائية بعد.</p>
                <a href="{% url 'patients:patient_list' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إنشاء خطة جديدة
                </a>
            </div>
        </div>
    {% endif %}
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card border-0 bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-utensils fa-2x mb-2"></i>
                <h4>{{ plans.paginator.count }}</h4>
                <small>إجمالي الخطط</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4>{{ plans.paginator.count }}</h4>
                <small>خطط نشطة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-pause-circle fa-2x mb-2"></i>
                <h4>0</h4>
                <small>خطط متوقفة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>{{ plans.paginator.count }}</h4>
                <small>مرضى مشتركون</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
