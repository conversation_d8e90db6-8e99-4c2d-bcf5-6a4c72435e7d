{% extends 'base.html' %}

{% block title %}الخطط الغذائية - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-utensils me-2"></i>الخطط الغذائية
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'nutrition_plans:select_patient_for_plan' %}" class="btn btn-success me-2">
            <i class="fas fa-plus me-2"></i>إنشاء خطة جديدة
        </a>
        <a href="{% url 'nutrition_plans:food_list' %}" class="btn btn-outline-primary">
            <i class="fas fa-apple-alt me-2"></i>إدارة الأطعمة
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                {{ search_form.search }}
            </div>
            <div class="col-md-3">
                {{ search_form.status }}
            </div>
            <div class="col-md-3">
                {{ search_form.patient }}
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Plans List - Simple Table View -->
<div class="card">
    <div class="card-body p-0">
        {% if plans %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الخطة الغذائية</th>
                            <th>المريض</th>
                            <th>الحالة</th>
                            <th>السعرات</th>
                            <th>الوجبات</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for plan in plans %}
                        <tr>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ plan.title }}</div>
                                    {% if plan.instructions %}
                                        <small class="text-muted">{{ plan.instructions|truncatewords:6 }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px; font-size: 14px;">
                                        {{ plan.patient.user.first_name|first }}{{ plan.patient.user.last_name|first }}
                                    </div>
                                    <div>
                                        <div class="fw-medium">{{ plan.patient.user.get_full_name }}</div>
                                        <small class="text-muted">{{ plan.patient.user.email }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{% if plan.status == 'active' %}success{% elif plan.status == 'completed' %}primary{% elif plan.status == 'paused' %}warning{% else %}secondary{% endif %}">
                                    {{ plan.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="fw-bold text-primary">{{ plan.target_calories|default:"--" }}</div>
                                    <small class="text-muted">سعرة</small>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="fw-bold text-success">{{ plan.meals.count }}</div>
                                    <small class="text-muted">وجبة</small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div>{{ plan.start_date|date:"Y-m-d" }}</div>
                                    {% if plan.end_date %}
                                        <small class="text-muted">{{ plan.end_date|date:"m-d" }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'nutrition_plans:nutrition_plan_detail' plan.id %}"
                                       class="btn btn-outline-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'nutrition_plans:edit_nutrition_plan' plan.id %}"
                                       class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'nutrition_plans:send_plan_whatsapp' plan.id %}"
                                       class="btn btn-outline-success" title="إرسال واتساب">
                                        <i class="fab fa-whatsapp"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد خطط غذائية</h5>
                <p class="text-muted">لم يتم إنشاء أي خطط غذائية بعد.</p>
                <a href="{% url 'nutrition_plans:select_patient_for_plan' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إنشاء خطة جديدة
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if plans.has_other_pages %}
    <nav aria-label="تنقل الخطط الغذائية" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if plans.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ plans.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابق</a>
                </li>
            {% endif %}

            {% for num in plans.paginator.page_range %}
                {% if plans.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > plans.number|add:'-3' and num < plans.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if plans.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ plans.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالي</a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa !important;
}

.table td {
    vertical-align: middle;
    border-color: #e9ecef;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

.fw-medium {
    font-weight: 500;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group-sm .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }

    .btn-group-sm .btn i {
        font-size: 0.75rem;
    }
}
</style>
{% endblock %}
