{% extends 'base.html' %}

{% block title %}اختيار مريض لإنشاء خطة غذائية - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-plus me-2"></i>اختيار مريض لإنشاء خطة غذائية</h2>
                <a href="{% url 'nutrition_plans:nutrition_plan_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للخطط الغذائية
                </a>
            </div>

            <!-- البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" name="search" class="form-control" 
                                       placeholder="البحث بالاسم أو البريد الإلكتروني..." 
                                       value="{{ search_query }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة المرضى -->
            {% if patients %}
                <div class="row">
                    {% for patient in patients %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="avatar-circle bg-primary text-white me-3">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <h6 class="card-title mb-1">{{ patient.user.get_full_name }}</h6>
                                            <small class="text-muted">{{ patient.user.email }}</small>
                                        </div>
                                    </div>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-4">
                                            <div class="border-end">
                                                <div class="fw-bold text-primary">{{ patient.age|default:"--" }}</div>
                                                <small class="text-muted">العمر</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="border-end">
                                                <div class="fw-bold text-success">{{ patient.current_weight|default:"--" }}</div>
                                                <small class="text-muted">الوزن</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="fw-bold text-info">{{ patient.height|default:"--" }}</div>
                                            <small class="text-muted">الطول</small>
                                        </div>
                                    </div>

                                    {% if patient.health_goal %}
                                        <div class="mb-3">
                                            <span class="badge bg-{% if patient.health_goal == 'weight_loss' %}danger{% elif patient.health_goal == 'weight_gain' %}success{% elif patient.health_goal == 'muscle_gain' %}primary{% else %}secondary{% endif %}">
                                                {{ patient.get_health_goal_display }}
                                            </span>
                                        </div>
                                    {% endif %}

                                    <!-- معلومات إضافية -->
                                    <div class="small text-muted mb-3">
                                        <div><i class="fas fa-calendar me-1"></i>انضم: {{ patient.user.date_joined|date:"Y-m-d" }}</div>
                                        {% if patient.nutrition_plans.count > 0 %}
                                            <div><i class="fas fa-utensils me-1"></i>الخطط السابقة: {{ patient.nutrition_plans.count }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <div class="d-grid">
                                        <a href="{% url 'nutrition_plans:create_nutrition_plan' patient.id %}" 
                                           class="btn btn-success">
                                            <i class="fas fa-plus me-2"></i>إنشاء خطة غذائية
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- الترقيم -->
                {% if patients.has_other_pages %}
                    <nav aria-label="تنقل المرضى" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if patients.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأول</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ patients.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابق</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">{{ patients.number }} من {{ patients.paginator.num_pages }}</span>
                            </li>

                            {% if patients.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ patients.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالي</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ patients.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخير</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مرضى</h5>
                    {% if search_query %}
                        <p class="text-muted">لم يتم العثور على مرضى بالبحث "{{ search_query }}"</p>
                        <a href="{% url 'nutrition_plans:select_patient_for_plan' %}" class="btn btn-outline-primary">
                            <i class="fas fa-times me-2"></i>إلغاء البحث
                        </a>
                    {% else %}
                        <p class="text-muted">لم يتم إضافة أي مرضى بعد.</p>
                        <a href="{% url 'patients:add_patient' %}" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>إضافة مريض جديد
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}

@media (max-width: 768px) {
    .border-end {
        border-right: none !important;
        border-bottom: 1px solid #dee2e6 !important;
        padding-bottom: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .border-end:last-child {
        border-bottom: none !important;
        padding-bottom: 0;
        margin-bottom: 0;
    }
}
</style>
{% endblock %}
