{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}تعديل {{ food.name_ar }} - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-edit me-2"></i>تعديل {{ food.name_ar }}</h2>
                <div>
                    <a href="{% url 'nutrition_plans:food_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة لقائمة الأطعمة
                    </a>
                </div>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">تعديل معلومات الطعام</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                <!-- أسماء الطعام -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">أسماء الطعام</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم بالعربية *</label>
                                        {{ form.name_ar|add_class:"form-control" }}
                                        {% if form.name_ar.errors %}
                                            <div class="text-danger small">{{ form.name_ar.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم بالإنجليزية *</label>
                                        {{ form.name_en|add_class:"form-control" }}
                                        {% if form.name_en.errors %}
                                            <div class="text-danger small">{{ form.name_en.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم بالكردية</label>
                                        {{ form.name_ku|add_class:"form-control" }}
                                        {% if form.name_ku.errors %}
                                            <div class="text-danger small">{{ form.name_ku.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الفئة *</label>
                                        {{ form.category|add_class:"form-select" }}
                                        {% if form.category.errors %}
                                            <div class="text-danger small">{{ form.category.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- القيم الغذائية -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">القيم الغذائية لكل 100 غرام</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">السعرات الحرارية *</label>
                                        <div class="input-group">
                                            {{ form.calories_per_100g|add_class:"form-control" }}
                                            <span class="input-group-text">سعرة</span>
                                        </div>
                                        {% if form.calories_per_100g.errors %}
                                            <div class="text-danger small">{{ form.calories_per_100g.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البروتين *</label>
                                        <div class="input-group">
                                            {{ form.protein_per_100g|add_class:"form-control" }}
                                            <span class="input-group-text">غ</span>
                                        </div>
                                        {% if form.protein_per_100g.errors %}
                                            <div class="text-danger small">{{ form.protein_per_100g.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الكربوهيدرات *</label>
                                        <div class="input-group">
                                            {{ form.carbs_per_100g|add_class:"form-control" }}
                                            <span class="input-group-text">غ</span>
                                        </div>
                                        {% if form.carbs_per_100g.errors %}
                                            <div class="text-danger small">{{ form.carbs_per_100g.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الدهون *</label>
                                        <div class="input-group">
                                            {{ form.fat_per_100g|add_class:"form-control" }}
                                            <span class="input-group-text">غ</span>
                                        </div>
                                        {% if form.fat_per_100g.errors %}
                                            <div class="text-danger small">{{ form.fat_per_100g.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الألياف</label>
                                        <div class="input-group">
                                            {{ form.fiber_per_100g|add_class:"form-control" }}
                                            <span class="input-group-text">غ</span>
                                        </div>
                                        {% if form.fiber_per_100g.errors %}
                                            <div class="text-danger small">{{ form.fiber_per_100g.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- أزرار التحكم -->
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'nutrition_plans:food_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- معلومات الطعام الحالية -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">المعلومات الحالية</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>الاسم العربي:</strong></td>
                                    <td>{{ food.name_ar }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم الإنجليزي:</strong></td>
                                    <td>{{ food.name_en }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الفئة:</strong></td>
                                    <td>{{ food.get_category_display }}</td>
                                </tr>
                                <tr>
                                    <td><strong>السعرات:</strong></td>
                                    <td>{{ food.calories_per_100g }} سعرة</td>
                                </tr>
                                <tr>
                                    <td><strong>البروتين:</strong></td>
                                    <td>{{ food.protein_per_100g }} غ</td>
                                </tr>
                                <tr>
                                    <td><strong>الكربوهيدرات:</strong></td>
                                    <td>{{ food.carbs_per_100g }} غ</td>
                                </tr>
                                <tr>
                                    <td><strong>الدهون:</strong></td>
                                    <td>{{ food.fat_per_100g }} غ</td>
                                </tr>
                                <tr>
                                    <td><strong>الألياف:</strong></td>
                                    <td>{{ food.fiber_per_100g }} غ</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإضافة:</strong></td>
                                    <td>{{ food.created_at|date:"Y-m-d" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">نصائح التعديل</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    تأكد من دقة القيم الغذائية
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    التعديل سيؤثر على جميع الخطط الغذائية
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-save text-success me-2"></i>
                                    احفظ التغييرات بعد التأكد
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
