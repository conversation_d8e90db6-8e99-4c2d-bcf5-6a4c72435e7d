{% extends 'base.html' %}

{% block title %}تفاصيل الموعد - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar me-2"></i>تفاصيل الموعد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if user.user_type == 'doctor' and appointment.status == 'scheduled' %}
                <a href="{% url 'communications:edit_appointment' appointment.id %}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>تعديل
                </a>
                <a href="{% url 'communications:cancel_appointment' appointment.id %}" class="btn btn-danger">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            {% endif %}
        </div>
        <a href="{% url 'communications:appointment_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للمواعيد
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-{% if appointment.status == 'scheduled' %}primary{% elif appointment.status == 'completed' %}success{% else %}danger{% endif %} text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar me-2"></i>معلومات الموعد
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المريض:</strong></td>
                                <td>{{ appointment.patient.user.get_full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>الطبيب:</strong></td>
                                <td>د. {{ appointment.doctor.get_full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ:</strong></td>
                                <td>{{ appointment.appointment_date|date:"Y-m-d" }}</td>
                            </tr>
                            <tr>
                                <td><strong>الوقت:</strong></td>
                                <td>{{ appointment.appointment_date|time:"H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المدة:</strong></td>
                                <td>{{ appointment.duration_minutes }} دقيقة</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge bg-{% if appointment.status == 'scheduled' %}primary{% elif appointment.status == 'completed' %}success{% else %}danger{% endif %} fs-6">
                                        {{ appointment.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ appointment.created_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ appointment.updated_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if appointment.notes %}
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2">الملاحظات:</h6>
                    <p class="text-muted">{{ appointment.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Patient Info -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>معلومات المريض
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if appointment.patient.user.profile_picture %}
                        <img src="{{ appointment.patient.user.profile_picture.url }}" class="rounded-circle" width="80" height="80" alt="صورة المريض">
                    {% else %}
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 80px; height: 80px; font-size: 2rem;">
                            {{ appointment.patient.user.first_name|first }}
                        </div>
                    {% endif %}
                    <h6 class="mt-2 mb-0">{{ appointment.patient.user.get_full_name }}</h6>
                    <small class="text-muted">{{ appointment.patient.user.get_user_type_display }}</small>
                </div>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>الهاتف:</strong></td>
                        <td>{{ appointment.patient.user.phone_number|default:"-" }}</td>
                    </tr>
                    <tr>
                        <td><strong>الواتساب:</strong></td>
                        <td>{{ appointment.patient.user.whatsapp_number|default:"-" }}</td>
                    </tr>
                    <tr>
                        <td><strong>البريد:</strong></td>
                        <td>{{ appointment.patient.user.email }}</td>
                    </tr>
                    {% if appointment.patient.age %}
                    <tr>
                        <td><strong>العمر:</strong></td>
                        <td>{{ appointment.patient.age }} سنة</td>
                    </tr>
                    {% endif %}
                </table>
                
                <div class="d-grid">
                    <a href="{% url 'patients:patient_detail' appointment.patient.id %}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-eye me-2"></i>عرض الملف الكامل
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        {% if user.user_type == 'doctor' %}
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'communications:send_message' appointment.patient.id %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-envelope me-2"></i>إرسال رسالة
                    </a>
                    <a href="{% url 'communications:send_whatsapp_message' appointment.patient.id %}" class="btn btn-outline-success btn-sm">
                        <i class="fab fa-whatsapp me-2"></i>رسالة واتساب
                    </a>
                    <a href="{% url 'nutrition_plans:create_nutrition_plan' appointment.patient.id %}" class="btn btn-outline-info btn-sm" title="إنشاء خطة غذائية">
                        <i class="fas fa-utensils me-2"></i>خطة غذائية
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
