{% extends 'base.html' %}

{% block title %}قائمة المرضى - اختبار{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users me-2"></i>إدارة المرضى</h2>
                <div>
                    <a href="{% url 'patients:test_add_patient' %}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>إضافة مريض جديد
                    </a>
                    <a href="/" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-2"></i>الرئيسية
                    </a>
                </div>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- البحث والتصفية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">البحث والتصفية</h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" name="search" 
                                   value="{{ search_query }}" placeholder="البحث بالاسم أو البريد...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الجنس</label>
                            <select class="form-select" name="gender">
                                <option value="">جميع الأجناس</option>
                                <option value="M" {% if gender_filter == 'M' %}selected{% endif %}>ذكر</option>
                                <option value="F" {% if gender_filter == 'F' %}selected{% endif %}>أنثى</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الهدف الصحي</label>
                            <select class="form-select" name="goal">
                                <option value="">جميع الأهداف</option>
                                <option value="lose_weight" {% if goal_filter == 'lose_weight' %}selected{% endif %}>إنقاص الوزن</option>
                                <option value="gain_weight" {% if goal_filter == 'gain_weight' %}selected{% endif %}>زيادة الوزن</option>
                                <option value="maintain_weight" {% if goal_filter == 'maintain_weight' %}selected{% endif %}>المحافظة على الوزن</option>
                                <option value="build_muscle" {% if goal_filter == 'build_muscle' %}selected{% endif %}>بناء العضلات</option>
                                <option value="improve_health" {% if goal_filter == 'improve_health' %}selected{% endif %}>تحسين الصحة العامة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة المرضى -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">قائمة المرضى</h5>
                    <span class="badge bg-primary">{{ patients|length }} مريض</span>
                </div>
                <div class="card-body">
                    {% if patients %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم المريض</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الجنس</th>
                                        <th>العمر</th>
                                        <th>الوزن الحالي</th>
                                        <th>الطول</th>
                                        <th>BMI</th>
                                        <th>الهدف</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for patient in patients %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    {{ patient.user.first_name|first|default:"؟" }}
                                                </div>
                                                <div>
                                                    <strong>{{ patient.user.get_full_name|default:patient.user.username }}</strong>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ patient.user.email }}</td>
                                        <td>
                                            <span class="badge bg-{% if patient.gender == 'M' %}primary{% else %}pink{% endif %}">
                                                {{ patient.get_gender_display }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if patient.user.date_of_birth %}
                                                {% now "Y" as current_year %}
                                                {{ current_year|add:"-"|add:patient.user.date_of_birth.year }} سنة
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>{{ patient.current_weight }} كغ</td>
                                        <td>{{ patient.height }} سم</td>
                                        <td>
                                            {% if patient.bmi %}
                                                <span class="badge bg-{% if patient.bmi < 18.5 %}warning{% elif patient.bmi < 25 %}success{% elif patient.bmi < 30 %}warning{% else %}danger{% endif %}">
                                                    {{ patient.bmi }}
                                                </span>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>{{ patient.get_health_goal_display }}</td>
                                        <td>{{ patient.created_at|date:"Y-m-d" }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{% url 'patients:test_patient_detail' patient.id %}"
                                                   class="btn btn-outline-primary btn-sm" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'patients:edit_patient' patient.id %}" 
                                                   class="btn btn-outline-warning btn-sm" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'patients:add_weight_record' patient.id %}" 
                                                   class="btn btn-outline-success btn-sm" title="إضافة وزن">
                                                    <i class="fas fa-weight"></i>
                                                </a>
                                                <a href="{% url 'patients:add_inbody_result' patient.id %}" 
                                                   class="btn btn-outline-info btn-sm" title="إضافة InBody">
                                                    <i class="fas fa-chart-bar"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- الترقيم -->
                        {% if patients.has_other_pages %}
                            <nav aria-label="ترقيم الصفحات" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if patients.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ patients.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if gender_filter %}&gender={{ gender_filter }}{% endif %}{% if goal_filter %}&goal={{ goal_filter }}{% endif %}">
                                                السابق
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for num in patients.paginator.page_range %}
                                        {% if patients.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% else %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if gender_filter %}&gender={{ gender_filter }}{% endif %}{% if goal_filter %}&goal={{ goal_filter }}{% endif %}">
                                                    {{ num }}
                                                </a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if patients.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ patients.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if gender_filter %}&gender={{ gender_filter }}{% endif %}{% if goal_filter %}&goal={{ goal_filter }}{% endif %}">
                                                التالي
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                            <h5>لا يوجد مرضى بعد</h5>
                            <p class="text-muted">ابدأ بإضافة مرضى جدد لعيادتك</p>
                            <a href="{% url 'patients:test_add_patient' %}" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>إضافة مريض جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 14px;
}
</style>
{% endblock %}
