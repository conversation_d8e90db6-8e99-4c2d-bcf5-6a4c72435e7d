<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة المرضى - نظام إدارة العيادات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-users me-2"></i>إدارة المرضى</h2>
            <div>
                <a href="/patients/simple-add/" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>إضافة مريض جديد
                </a>
                <a href="/" class="btn btn-outline-secondary">
                    <i class="fas fa-home me-2"></i>الرئيسية
                </a>
            </div>
        </div>

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- البحث والتصفية -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               value="{{ search_query }}" placeholder="البحث بالاسم أو البريد...">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="gender">
                            <option value="">جميع الأجناس</option>
                            <option value="M" {% if gender_filter == 'M' %}selected{% endif %}>ذكر</option>
                            <option value="F" {% if gender_filter == 'F' %}selected{% endif %}>أنثى</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="goal">
                            <option value="">جميع الأهداف</option>
                            <option value="lose_weight" {% if goal_filter == 'lose_weight' %}selected{% endif %}>إنقاص الوزن</option>
                            <option value="gain_weight" {% if goal_filter == 'gain_weight' %}selected{% endif %}>زيادة الوزن</option>
                            <option value="maintain_weight" {% if goal_filter == 'maintain_weight' %}selected{% endif %}>المحافظة على الوزن</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- قائمة المرضى -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">قائمة المرضى ({{ patients|length }})</h5>
            </div>
            <div class="card-body">
                {% if patients %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>اسم المريض</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الجنس</th>
                                    <th>الوزن الحالي</th>
                                    <th>الطول</th>
                                    <th>BMI</th>
                                    <th>الهدف</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for patient in patients %}
                                <tr>
                                    <td>
                                        <strong>{{ patient.user.get_full_name|default:patient.user.username }}</strong>
                                    </td>
                                    <td>{{ patient.user.email }}</td>
                                    <td>
                                        <span class="badge bg-{% if patient.gender == 'M' %}primary{% else %}pink{% endif %}">
                                            {{ patient.get_gender_display }}
                                        </span>
                                    </td>
                                    <td>{{ patient.current_weight }} كغ</td>
                                    <td>{{ patient.height }} سم</td>
                                    <td>
                                        {% if patient.bmi %}
                                            <span class="badge bg-{% if patient.bmi < 18.5 %}warning{% elif patient.bmi < 25 %}success{% elif patient.bmi < 30 %}warning{% else %}danger{% endif %}">
                                                {{ patient.bmi }}
                                            </span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>{{ patient.get_health_goal_display }}</td>
                                    <td>{{ patient.created_at|date:"Y-m-d" }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="/patients/{{ patient.id }}/" class="btn btn-outline-primary btn-sm" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/patients/{{ patient.id }}/edit/" class="btn btn-outline-warning btn-sm" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- الترقيم -->
                    {% if patients.has_other_pages %}
                        <nav aria-label="ترقيم الصفحات">
                            <ul class="pagination justify-content-center">
                                {% if patients.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ patients.previous_page_number }}">السابق</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in patients.paginator.page_range %}
                                    {% if patients.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if patients.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ patients.next_page_number }}">التالي</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                        <h5>لا يوجد مرضى بعد</h5>
                        <p class="text-muted">ابدأ بإضافة مرضى جدد لعيادتك</p>
                        <a href="/patients/simple-add/" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>إضافة مريض جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
