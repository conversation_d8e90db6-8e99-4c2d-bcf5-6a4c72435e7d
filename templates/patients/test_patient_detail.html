{% extends 'base.html' %}

{% block title %}{{ patient.user.get_full_name }} - تفاصيل المريض{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user me-2"></i>{{ patient.user.get_full_name|default:patient.user.username }}</h2>
                <div>
                    <a href="{% url 'patients:edit_patient' patient.id %}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>تعديل البيانات
                    </a>
                    <a href="{% url 'patients:test_patient_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="row">
                <!-- معلومات المريض الأساسية -->
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">المعلومات الأساسية</h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <div class="avatar-lg bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center">
                                    <i class="fas fa-user fa-2x"></i>
                                </div>
                                <h5 class="mt-2">{{ patient.user.get_full_name|default:patient.user.username }}</h5>
                                <span class="badge bg-{% if patient.gender == 'M' %}primary{% else %}pink{% endif %}">
                                    {{ patient.get_gender_display }}
                                </span>
                            </div>
                            
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>البريد الإلكتروني:</strong></td>
                                    <td>{{ patient.user.email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>رقم الهاتف:</strong></td>
                                    <td>{{ patient.user.phone_number|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>رقم الواتساب:</strong></td>
                                    <td>{{ patient.user.whatsapp_number|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الميلاد:</strong></td>
                                    <td>{{ patient.user.date_of_birth|default:"-" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ التسجيل:</strong></td>
                                    <td>{{ patient.created_at|date:"Y-m-d" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- إجراءات سريعة -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">إجراءات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{% url 'patients:add_weight_record' patient.id %}" class="btn btn-outline-primary">
                                    <i class="fas fa-weight me-2"></i>إضافة سجل وزن
                                </a>
                                <a href="{% url 'patients:add_inbody_result' patient.id %}" class="btn btn-outline-success">
                                    <i class="fas fa-chart-bar me-2"></i>إضافة نتيجة InBody
                                </a>
                                <a href="#" class="btn btn-outline-info">
                                    <i class="fas fa-utensils me-2"></i>إنشاء خطة غذائية
                                </a>
                                <a href="{% url 'patients:patient_progress' patient.id %}" class="btn btn-outline-warning">
                                    <i class="fas fa-chart-line me-2"></i>تقرير التطور
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المعلومات التفصيلية -->
                <div class="col-lg-8">
                    <!-- المعلومات الجسدية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">المعلومات الجسدية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-primary">{{ patient.current_weight }}</h4>
                                        <small class="text-muted">الوزن الحالي (كغ)</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-info">{{ patient.height }}</h4>
                                        <small class="text-muted">الطول (سم)</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        {% if patient.bmi %}
                                            <h4 class="text-{% if patient.bmi < 18.5 %}warning{% elif patient.bmi < 25 %}success{% elif patient.bmi < 30 %}warning{% else %}danger{% endif %}">
                                                {{ patient.bmi }}
                                            </h4>
                                            <small class="text-muted">مؤشر كتلة الجسم</small>
                                            <br>
                                            <small class="badge bg-{% if patient.bmi < 18.5 %}warning{% elif patient.bmi < 25 %}success{% elif patient.bmi < 30 %}warning{% else %}danger{% endif %}">
                                                {{ patient.bmi_category }}
                                            </small>
                                        {% else %}
                                            <h4 class="text-muted">-</h4>
                                            <small class="text-muted">مؤشر كتلة الجسم</small>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-secondary">{{ patient.target_weight|default:"-" }}</h4>
                                        <small class="text-muted">الوزن المستهدف (كغ)</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأهداف والنشاط -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">الأهداف ومستوى النشاط</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>الهدف الصحي:</strong>
                                    <span class="badge bg-primary">{{ patient.get_health_goal_display }}</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>مستوى النشاط:</strong>
                                    <span class="badge bg-info">{{ patient.get_activity_level_display }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الحالة الصحية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">الحالة الصحية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <strong>الأمراض المزمنة:</strong>
                                    <p class="text-muted">{{ patient.medical_conditions|default:"لا توجد أمراض مزمنة مسجلة" }}</p>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <strong>الحساسيات الغذائية:</strong>
                                    <p class="text-muted">{{ patient.allergies|default:"لا توجد حساسيات مسجلة" }}</p>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <strong>الأدوية الحالية:</strong>
                                    <p class="text-muted">{{ patient.medications|default:"لا توجد أدوية مسجلة" }}</p>
                                </div>
                                {% if patient.notes %}
                                <div class="col-md-12">
                                    <strong>ملاحظات إضافية:</strong>
                                    <p class="text-muted">{{ patient.notes }}</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- آخر سجلات الوزن -->
                    {% if weight_records %}
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">آخر سجلات الوزن</h5>
                            <a href="{% url 'patients:add_weight_record' patient.id %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-plus me-1"></i>إضافة سجل
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الوزن (كغ)</th>
                                            <th>التغيير</th>
                                            <th>ملاحظات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for record in weight_records %}
                                        <tr>
                                            <td>{{ record.recorded_date|date:"Y-m-d" }}</td>
                                            <td>{{ record.weight }}</td>
                                            <td>
                                                {% if forloop.counter0 < weight_records|length|add:"-1" %}
                                                    {% with next_record=weight_records|slice:forloop.counter0|first %}
                                                        {% if record.weight > next_record.weight %}
                                                            <span class="text-success">+{{ record.weight|floatformat:1|add:"-"|add:next_record.weight|floatformat:1 }}</span>
                                                        {% elif record.weight < next_record.weight %}
                                                            <span class="text-danger">{{ record.weight|floatformat:1|add:"-"|add:next_record.weight|floatformat:1 }}</span>
                                                        {% else %}
                                                            <span class="text-muted">0</span>
                                                        {% endif %}
                                                    {% endwith %}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>{{ record.notes|default:"-" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- آخر نتائج InBody -->
                    {% if latest_inbody %}
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">آخر نتيجة InBody</h5>
                            <small class="text-muted">{{ latest_inbody.test_date|date:"Y-m-d" }}</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6 class="text-primary">{{ latest_inbody.muscle_mass }}</h6>
                                        <small>كتلة العضلات (كغ)</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6 class="text-warning">{{ latest_inbody.body_fat_mass }}</h6>
                                        <small>كتلة الدهون (كغ)</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6 class="text-info">{{ latest_inbody.body_fat_percentage }}%</h6>
                                        <small>نسبة الدهون</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6 class="text-success">{{ latest_inbody.basal_metabolic_rate|default:"-" }}</h6>
                                        <small>معدل الأيض الأساسي</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
    font-size: 24px;
}
</style>
{% endblock %}
