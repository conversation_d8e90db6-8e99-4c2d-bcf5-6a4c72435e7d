{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}إضافة مريض جديد - اختبار{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-plus me-2"></i>إضافة مريض جديد</h2>
                <a href="{% url 'patients:test_patient_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معلومات المريض</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- المعلومات الشخصية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">المعلومات الشخصية</h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الأول *</label>
                                {{ form.first_name|add_class:"form-control" }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small">{{ form.first_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم العائلة *</label>
                                {{ form.last_name|add_class:"form-control" }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small">{{ form.last_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني *</label>
                                {{ form.email|add_class:"form-control" }}
                                {% if form.email.errors %}
                                    <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                {{ form.phone_number|add_class:"form-control" }}
                                {% if form.phone_number.errors %}
                                    <div class="text-danger small">{{ form.phone_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الواتساب</label>
                                {{ form.whatsapp_number|add_class:"form-control" }}
                                {% if form.whatsapp_number.errors %}
                                    <div class="text-danger small">{{ form.whatsapp_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ الميلاد</label>
                                {{ form.date_of_birth|add_class:"form-control" }}
                                {% if form.date_of_birth.errors %}
                                    <div class="text-danger small">{{ form.date_of_birth.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- المعلومات الجسدية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">المعلومات الجسدية</h6>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الجنس *</label>
                                {{ form.gender|add_class:"form-select" }}
                                {% if form.gender.errors %}
                                    <div class="text-danger small">{{ form.gender.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الطول (سم) *</label>
                                {{ form.height|add_class:"form-control" }}
                                {% if form.height.errors %}
                                    <div class="text-danger small">{{ form.height.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الوزن الحالي (كغ) *</label>
                                {{ form.current_weight|add_class:"form-control" }}
                                {% if form.current_weight.errors %}
                                    <div class="text-danger small">{{ form.current_weight.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الوزن المستهدف (كغ)</label>
                                {{ form.target_weight|add_class:"form-control" }}
                                {% if form.target_weight.errors %}
                                    <div class="text-danger small">{{ form.target_weight.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">مستوى النشاط</label>
                                {{ form.activity_level|add_class:"form-select" }}
                                {% if form.activity_level.errors %}
                                    <div class="text-danger small">{{ form.activity_level.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">الهدف الصحي</label>
                                {{ form.health_goal|add_class:"form-select" }}
                                {% if form.health_goal.errors %}
                                    <div class="text-danger small">{{ form.health_goal.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- الحالة الصحية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">الحالة الصحية</h6>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">الأمراض المزمنة</label>
                                {{ form.medical_conditions|add_class:"form-control" }}
                                {% if form.medical_conditions.errors %}
                                    <div class="text-danger small">{{ form.medical_conditions.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">الحساسيات الغذائية</label>
                                {{ form.allergies|add_class:"form-control" }}
                                {% if form.allergies.errors %}
                                    <div class="text-danger small">{{ form.allergies.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">الأدوية الحالية</label>
                                {{ form.medications|add_class:"form-control" }}
                                {% if form.medications.errors %}
                                    <div class="text-danger small">{{ form.medications.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">ملاحظات إضافية</label>
                                {{ form.notes|add_class:"form-control" }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'patients:test_patient_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ المريض
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
