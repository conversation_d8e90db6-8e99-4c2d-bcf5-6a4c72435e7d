<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مريض جديد - نظام إدارة العيادات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-user-plus me-2"></i>إضافة مريض جديد</h4>
                    </div>
                    <div class="card-body">
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post">
                            {% csrf_token %}
                            
                            <!-- معلومات المستخدم -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">الاسم الأول *</label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger">{{ form.first_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">الاسم الأخير *</label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger">{{ form.last_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">البريد الإلكتروني *</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">رقم الهاتف</label>
                                    {{ form.phone_number }}
                                    {% if form.phone_number.errors %}
                                        <div class="text-danger">{{ form.phone_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- المعلومات الجسدية -->
                            <h5 class="text-primary mb-3">المعلومات الجسدية</h5>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">الجنس *</label>
                                    {{ form.gender }}
                                    {% if form.gender.errors %}
                                        <div class="text-danger">{{ form.gender.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">الطول (سم) *</label>
                                    {{ form.height }}
                                    {% if form.height.errors %}
                                        <div class="text-danger">{{ form.height.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">الوزن الحالي (كغ) *</label>
                                    {{ form.current_weight }}
                                    {% if form.current_weight.errors %}
                                        <div class="text-danger">{{ form.current_weight.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">مستوى النشاط</label>
                                    {{ form.activity_level }}
                                    {% if form.activity_level.errors %}
                                        <div class="text-danger">{{ form.activity_level.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">الهدف الصحي</label>
                                    {{ form.health_goal }}
                                    {% if form.health_goal.errors %}
                                        <div class="text-danger">{{ form.health_goal.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- الحالة الصحية -->
                            <h5 class="text-primary mb-3">الحالة الصحية</h5>
                            <div class="mb-3">
                                <label class="form-label">الأمراض المزمنة</label>
                                {{ form.medical_conditions }}
                                {% if form.medical_conditions.errors %}
                                    <div class="text-danger">{{ form.medical_conditions.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الحساسيات الغذائية</label>
                                {{ form.allergies }}
                                {% if form.allergies.errors %}
                                    <div class="text-danger">{{ form.allergies.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label class="form-label">ملاحظات إضافية</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- أزرار التحكم -->
                            <div class="d-flex justify-content-between">
                                <a href="/patients/" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right me-2"></i>العودة
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ المريض
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
