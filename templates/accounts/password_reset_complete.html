{% extends 'base.html' %}

{% block title %}تم تغيير كلمة المرور - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-success text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>تم بنجاح!
                    </h4>
                </div>
                <div class="card-body p-4 text-center">
                    <div class="mb-4">
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <h5 class="text-success">تم تغيير كلمة المرور بنجاح!</h5>
                        <p class="text-muted">
                            لقد تم تحديث كلمة المرور الخاصة بك. يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.
                        </p>
                    </div>

                    <div class="alert alert-success">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>نصائح أمنية:</strong>
                        <ul class="list-unstyled mt-2 mb-0 small">
                            <li>• احتفظ بكلمة المرور في مكان آمن</li>
                            <li>• لا تشارك كلمة المرور مع أي شخص</li>
                            <li>• قم بتغيير كلمة المرور بانتظام</li>
                            <li>• استخدم كلمات مرور قوية ومختلفة</li>
                        </ul>
                    </div>

                    <div class="d-grid">
                        <a href="{% url 'login' %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول الآن
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    border-bottom: none;
    padding: 1.5rem;
}

.alert {
    border-radius: 10px;
    border: none;
    text-align: right;
}

.btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

@media (max-width: 576px) {
    .container {
        padding: 1rem;
    }
    
    .card-body {
        padding: 2rem 1.5rem !important;
    }
}
</style>
{% endblock %}
