{% extends 'base.html' %}

{% block title %}تم إرسال رابط إعادة التعيين - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-success text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>تم إرسال الرابط
                    </h4>
                </div>
                <div class="card-body p-4 text-center">
                    <div class="mb-4">
                        <i class="fas fa-envelope-open fa-3x text-success mb-3"></i>
                        <h5 class="text-success">تم إرسال البريد بنجاح!</h5>
                        <p class="text-muted">
                            لقد أرسلنا لك رابط إعادة تعيين كلمة المرور على بريدك الإلكتروني.
                        </p>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>تعليمات:</strong>
                        <ul class="list-unstyled mt-2 mb-0">
                            <li>• تحقق من صندوق الوارد في بريدك الإلكتروني</li>
                            <li>• إذا لم تجد الرسالة، تحقق من مجلد الرسائل المزعجة</li>
                            <li>• الرابط صالح لمدة 24 ساعة فقط</li>
                        </ul>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="{% url 'login' %}" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>العودة لتسجيل الدخول
                        </a>
                        <a href="{% url 'password_reset' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-redo me-2"></i>إرسال رابط آخر
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    border-bottom: none;
    padding: 1.5rem;
}

.alert {
    border-radius: 10px;
    border: none;
    text-align: right;
}

.btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

@media (max-width: 576px) {
    .container {
        padding: 1rem;
    }
    
    .card-body {
        padding: 2rem 1.5rem !important;
    }
}
</style>
{% endblock %}
