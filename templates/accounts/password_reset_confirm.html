{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}تعيين كلمة مرور جديدة - نظام إدارة العيادات الغذائية{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm border-0">
                {% if validlink %}
                    <div class="card-header bg-primary text-white text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-lock me-2"></i>كلمة مرور جديدة
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <p class="text-muted">
                                أدخل كلمة المرور الجديدة لحسابك
                            </p>
                        </div>

                        <form method="post">
                            {% csrf_token %}
                            
                            <div class="mb-3">
                                <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                                    <i class="fas fa-key me-1"></i>كلمة المرور الجديدة
                                </label>
                                {{ form.new_password1|add_class:"form-control"|attr:"placeholder:أدخل كلمة المرور الجديدة" }}
                                {% if form.new_password1.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.new_password1.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                                    <i class="fas fa-key me-1"></i>تأكيد كلمة المرور
                                </label>
                                {{ form.new_password2|add_class:"form-control"|attr:"placeholder:أعد إدخال كلمة المرور" }}
                                {% if form.new_password2.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.new_password2.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>متطلبات كلمة المرور:</strong>
                                <ul class="list-unstyled mt-2 mb-0 small">
                                    <li>• يجب أن تحتوي على 8 أحرف على الأقل</li>
                                    <li>• لا يجب أن تكون شائعة جداً</li>
                                    <li>• لا يجب أن تكون مشابهة لمعلوماتك الشخصية</li>
                                </ul>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ كلمة المرور الجديدة
                                </button>
                            </div>
                        </form>
                    </div>
                {% else %}
                    <div class="card-header bg-danger text-white text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>رابط غير صالح
                        </h4>
                    </div>
                    <div class="card-body p-4 text-center">
                        <div class="mb-4">
                            <i class="fas fa-times-circle fa-3x text-danger mb-3"></i>
                            <h5 class="text-danger">الرابط غير صالح أو منتهي الصلاحية</h5>
                            <p class="text-muted">
                                قد يكون الرابط قد انتهت صلاحيته أو تم استخدامه مسبقاً.
                            </p>
                        </div>

                        <div class="d-grid gap-2">
                            <a href="{% url 'password_reset' %}" class="btn btn-primary">
                                <i class="fas fa-redo me-2"></i>طلب رابط جديد
                            </a>
                            <a href="{% url 'login' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>العودة لتسجيل الدخول
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    border-bottom: none;
    padding: 1.5rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.alert {
    border-radius: 10px;
    border: none;
    text-align: right;
}

.btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

@media (max-width: 576px) {
    .container {
        padding: 1rem;
    }
    
    .card-body {
        padding: 2rem 1.5rem !important;
    }
}
</style>
{% endblock %}
